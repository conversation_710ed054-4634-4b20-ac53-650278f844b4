import { But<PERSON> } from "@/components/ui/button";
import { Plus, Minus, Trash2, Edit3, PlusCircle } from "lucide-react";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useEffect, useState } from "react";
import { useLazyGetModifierMenusQuery } from "@/redux/slices/menuitemmodifier";

type Props = {
  item: any;
  noteText: string;
  calculateItemTotal: (item: any) => number;
  updateQuantity: (itemId: string, delta: number) => void;
  setOrderItems: React.Dispatch<React.SetStateAction<any[]>>;
  setNoteText: React.Dispatch<React.SetStateAction<string>>;
};

// Mock extras data
const availableExtras = [
  { id: "extra1", label: "Extra Sugar", price: 0.5 },
  { id: "extra2", label: "Extra Cream", price: 0.75 },
  { id: "extra3", label: "Extra Shot", price: 1.0 },
  { id: "extra4", label: "Caramel Syrup", price: 0.8 },
  { id: "extra5", label: "Vanilla Syrup", price: 0.8 },
];

const StationOrderItem = ({
  item,
  noteText,
  calculateItemTotal,
  updateQuantity,
  setOrderItems,
  setNoteText,
}: Props) => {
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  const [
    getItemModifiers,
    { data: modifiers, isLoading: mdLoading, isFetching: mdFetching },
  ] = useLazyGetModifierMenusQuery();

  const handleFetchExtras = async () => {
    await getItemModifiers({ menu_item: item.id }).unwrap();
  };

  const handleAddNote = (itemId: string) => {
    setOrderItems((prev) =>
      prev.map((item) =>
        item.id === itemId ? { ...item, notes: noteText } : item
      )
    );
    setNoteText("");
    setSelectedItemId(null); // Close the modal by resetting selectedItemId
  };

  const handleAddExtras = (
    itemId: string,
    extra: { label: string; price: string; id: string },
    checked: boolean
  ) => {
    setOrderItems((prev) =>
      prev.map((item) => {
        if (item.id === itemId) {
          const currentExtras = item.extras || [];
          if (checked) {
            return {
              ...item,
              unit_price: item.price,
              extras: [...currentExtras, extra],
              price: item.price + extra?.price,
            };
          } else {
            return {
              ...item,
              unit_price: item.price,
              extras: currentExtras.filter((e: any) => e.id !== extra?.id),
              price: parseFloat(item.price) - parseFloat(extra?.price),
            };
          }
        }
        return item;
      })
    );
  };

  return (
    <div className="flex flex-col gap-2 sm:gap-3 mb-3 sm:mb-4 p-3 sm:p-4 border border-border/50 rounded-lg hover:shadow-lg transition-all duration-300 bg-card/50 backdrop-blur-sm mobile-compact">
      <div className="flex justify-between items-start gap-2">
        <span className="font-bold text-xs sm:text-sm text-foreground leading-tight flex-1">{item.name}</span>
        <span className="text-primary text-xs sm:text-sm font-bold bg-primary/10 px-2 py-1 rounded-md whitespace-nowrap">
          Ksh. {calculateItemTotal(item)}
        </span>
      </div>

      <div className="flex flex-col gap-2 sm:gap-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 sm:gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => updateQuantity(item.id, -1)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:scale-110 transition-all duration-200 border-border/50 hover:border-destructive/50 hover:text-destructive"
            >
              <Minus className="h-3 w-3" />
            </Button>
            <Input
              type="number"
              value={item.quantity}
              onChange={(e) => {
                const newQuantity = parseInt(e.target.value) || 0;
                updateQuantity(item.id, newQuantity - item.quantity);
              }}
              className="w-12 sm:w-16 text-center h-7 sm:h-8 text-xs sm:text-sm border-border/50 bg-background/50"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => updateQuantity(item.id, 1)}
              className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:scale-110 transition-all duration-200 border-border/50 hover:border-primary/50 hover:text-primary"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>

          <Button
            size="sm"
            variant="destructive"
            onClick={() => updateQuantity(item.id, -item.quantity)}
            className="h-7 w-7 sm:h-8 sm:w-8 p-0 hover:scale-110 transition-all duration-200 shadow-md"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>

        {item.notes && (
          <div className="text-xs text-muted-foreground bg-muted/50 p-2 rounded-md border border-border/30">
            <span className="font-medium">Note:</span> {item.notes}
          </div>
        )}

        {item.extras && item.extras.length > 0 && (
          <div className="text-xs text-muted-foreground bg-secondary/10 p-2 rounded-md border border-secondary/20">
            <span className="font-medium">Extras:</span>{" "}
            {item.extras
              .map((extra: any) => `${extra?.label} (+Ksh. ${extra?.price})`)
              .join(", ")}
          </div>
        )}

        <div className="flex gap-1 sm:gap-2 flex-wrap">
          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleFetchExtras()}
                className="h-7 sm:h-8 hover:scale-105 transition-all duration-200 border-border/50 hover:border-secondary/50 hover:text-secondary px-2 sm:px-3"
              >
                <PlusCircle className="h-3 w-3 mr-1" />
                <span className="text-xs">Extras</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] max-w-[95vw] border-border/50">
              <DialogHeader>
                <DialogTitle className="text-foreground text-sm sm:text-base">
                  Add {item?.name} Extras
                </DialogTitle>
              </DialogHeader>
              <div className="grid gap-3 py-4 max-h-[60vh] overflow-y-auto">
                {mdLoading || mdFetching ? (
                  <div className="text-center text-muted-foreground text-sm">
                    Loading extras...
                  </div>
                ) : modifiers?.data?.total_data < 1 ? (
                  <div className="text-center text-muted-foreground text-sm">
                    No extras available
                  </div>
                ) : (
                  modifiers?.data?.results?.map((extra: any) => (
                    <div
                      key={extra?.id}
                      className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      <Checkbox
                        id={extra?.id}
                        checked={item?.extras?.some(
                          (e: any) => e.id === extra?.id
                        )}
                        onCheckedChange={(checked) => {
                          handleAddExtras(
                            item.id,
                            {
                              label: extra?.name,
                              price: extra?.price,
                              id: extra?.id,
                            },
                            checked as boolean
                          );
                        }}
                        className="border-border/50"
                      />
                      <Label
                        htmlFor={extra?.id}
                        className="text-foreground cursor-pointer flex-1 text-sm"
                      >
                        {extra?.name}
                        <span className="text-primary font-semibold ml-2">
                          (+ Ksh. {extra?.price})
                        </span>
                      </Label>
                    </div>
                  ))
                )}
              </div>
            </DialogContent>
          </Dialog>

          <Dialog>
            <DialogTrigger asChild>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setSelectedItemId(item.id);
                  setNoteText(item.notes || "");
                }}
                className="h-7 sm:h-8 hover:scale-105 transition-all duration-200 border-border/50 hover:border-blue-500/50 hover:text-blue-600 px-2 sm:px-3"
              >
                <Edit3 className="h-3 w-3 mr-1" />
                <span className="text-xs">Note</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] max-w-[95vw] border-border/50">
              <DialogHeader>
                <DialogTitle className="text-foreground text-sm sm:text-base">Add Note</DialogTitle>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <Textarea
                  placeholder="Add special instructions..."
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                  className="min-h-[80px] sm:min-h-[100px] border-border/50 bg-background/50 text-sm"
                />
                <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-3 justify-end">
                  <DialogClose asChild>
                    <Button
                      variant="outline"
                      onClick={() => {
                        setNoteText("");
                        setSelectedItemId(null);
                      }}
                      className="w-full sm:w-auto hover:scale-105 transition-all duration-200 text-sm"
                    >
                      Cancel
                    </Button>
                  </DialogClose>
                  <Button
                    onClick={() => handleAddNote(item.id)}
                    className="w-full sm:w-auto hover:scale-105 transition-all duration-200 text-sm"
                  >
                    Save Note
                  </Button>
                </DialogFooter>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  );
};

export default StationOrderItem;
